import 'package:flutter/material.dart';

class UIHelper {
  UIHelper._();

  static double getAppTitleWidgetHeight() {
    return 120.0; // Fixed height instead of responsive
  }

  static final Map<String, Color> _typeColorMap = {
    'Grass': Colors.green,
    'Fire': Colors.redAccent,
    'Water': Colors.blue,
    'Electric': Colors.yellow,
    'Rock': Colors.grey,
    'Ground': Colors.brown,
    'Bug': Colors.lightGreenAccent.shade700,
    'Psychic': Colors.indigo,
    'Fighting': Colors.orange,
    'Ghost': Colors.deepPurple,
    'Normal': Colors.black26,
    'Poison': Colors.deepPurpleAccent,
  };

  static Color getColorFromType(String type) {
    if (_typeColorMap.containsKey(type)) {
      return _typeColorMap[type] ?? Colors.pink.shade300;
    } else {
      return Colors.pink.shade300;
    }
  }

  static EdgeInsets getDefaultPadding() {
    return const EdgeInsets.all(12.0);
  }

  static EdgeInsets getIconPadding() {
    return const EdgeInsets.all(10.0);
  }

  static double calculatePokeImgAndBallSize() {
    return 80.0; // Fixed size instead of responsive
  }
}
